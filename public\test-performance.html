<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>DOM Rendering Performance Test</title>
  <link rel="stylesheet" href="style.css">
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #222;
      color: #fff;
      margin: 0;
      padding: 20px;
    }
    .test-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    .test-result {
      margin: 10px 0;
      padding: 10px;
      border-radius: 4px;
    }
    .success { background: #2d5a2d; }
    .error { background: #5a2d2d; }
    .info { background: #2d2d5a; }
    .warning { background: #5a5a2d; }
    .performance-metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 15px;
      margin: 20px 0;
    }
    .metric-card {
      background: rgba(255, 255, 255, 0.1);
      padding: 15px;
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .metric-value {
      font-size: 24px;
      font-weight: bold;
      color: #4ECDC4;
    }
    .game-container {
      display: flex;
      gap: 20px;
      margin-top: 20px;
    }
    .game-instance {
      flex: 1;
      border: 2px solid #666;
      border-radius: 8px;
      padding: 10px;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>🚀 Bomberman DOM Performance Test</h1>
    <div id="test-results"></div>
    <div class="performance-metrics" id="metrics"></div>
    <div class="game-container" id="game-container"></div>
  </div>

  <script type="module">
    const results = document.getElementById('test-results');
    const metricsContainer = document.getElementById('metrics');
    const gameContainer = document.getElementById('game-container');

    let performanceData = {
      fps: 0,
      frameTime: 0,
      domElements: 0,
      memoryUsage: 0,
      renderTime: 0
    };

    function log(message, type = 'info') {
      const div = document.createElement('div');
      div.className = `test-result ${type}`;
      div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
      results.appendChild(div);
      console.log(message);
    }

    function updateMetrics() {
      metricsContainer.innerHTML = `
        <div class="metric-card">
          <div class="metric-value">${performanceData.fps}</div>
          <div>FPS</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${performanceData.frameTime.toFixed(2)}ms</div>
          <div>Frame Time</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${performanceData.domElements}</div>
          <div>DOM Elements</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${performanceData.memoryUsage.toFixed(1)}MB</div>
          <div>Memory Usage</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${performanceData.renderTime.toFixed(2)}ms</div>
          <div>Render Time</div>
        </div>
      `;
    }

    function measurePerformance() {
      // FPS measurement
      let frameCount = 0;
      let lastTime = performance.now();
      
      function measureFPS() {
        frameCount++;
        const currentTime = performance.now();
        const deltaTime = currentTime - lastTime;
        
        if (deltaTime >= 1000) {
          performanceData.fps = Math.round((frameCount * 1000) / deltaTime);
          performanceData.frameTime = deltaTime / frameCount;
          frameCount = 0;
          lastTime = currentTime;
        }
        
        requestAnimationFrame(measureFPS);
      }
      
      measureFPS();
      
      // Memory usage
      if (performance.memory) {
        performanceData.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024;
      }
      
      // DOM elements count
      performanceData.domElements = document.querySelectorAll('*').length;
      
      updateMetrics();
    }

    async function runPerformanceTests() {
      log('🚀 Starting comprehensive performance tests...', 'info');

      try {
        // Import renderer
        const renderer = await import('./src/game/renderer.js');
        log('✅ Renderer module loaded', 'success');

        // Create multiple game instances for stress testing
        for (let i = 0; i < 3; i++) {
          const gameInstance = document.createElement('div');
          gameInstance.className = 'game-instance';
          gameInstance.innerHTML = `<h3>Game Instance ${i + 1}</h3>`;
          gameContainer.appendChild(gameInstance);

          // Create complex game state
          const complexState = {
            map: Array(15).fill().map(() => 
              Array(17).fill().map(() => Math.random() > 0.7 ? 'W' : Math.random() > 0.8 ? 'B' : 'E')
            ),
            players: Array(4).fill().map((_, idx) => ({
              id: `player${idx}`,
              nickname: `Player${idx + 1}`,
              row: Math.floor(Math.random() * 15),
              col: Math.floor(Math.random() * 17),
              alive: true
            })),
            bombs: Array(8).fill().map((_, idx) => ({
              id: `bomb${idx}`,
              row: Math.floor(Math.random() * 15),
              col: Math.floor(Math.random() * 17),
              timer: 3000,
              exploded: false
            })),
            explosions: Array(12).fill().map((_, idx) => ({
              id: `explosion${idx}`,
              row: Math.floor(Math.random() * 15),
              col: Math.floor(Math.random() * 17)
            })),
            powerups: Array(6).fill().map((_, idx) => ({
              id: `powerup${idx}`,
              type: ['speed_up', 'bomb_up', 'flame_up'][Math.floor(Math.random() * 3)],
              row: Math.floor(Math.random() * 15),
              col: Math.floor(Math.random() * 17),
              collected: false
            }))
          };

          // Render game instance
          const startTime = performance.now();
          renderer.renderGame(complexState, gameInstance);
          const renderTime = performance.now() - startTime;
          performanceData.renderTime = Math.max(performanceData.renderTime, renderTime);

          log(`✅ Game instance ${i + 1} rendered in ${renderTime.toFixed(2)}ms`, 'success');
        }

        // Start continuous performance monitoring
        measurePerformance();
        setInterval(measurePerformance, 1000);

        // Stress test with rapid updates
        let updateCount = 0;
        function stressTest() {
          if (updateCount < 100) {
            // Update random game elements
            const instances = document.querySelectorAll('.game-instance');
            instances.forEach(instance => {
              const players = instance.querySelectorAll('.game-object.player');
              players.forEach(player => {
                const newX = Math.random() * 500;
                const newY = Math.random() * 400;
                player.style.left = newX + 'px';
                player.style.top = newY + 'px';
              });
            });
            updateCount++;
            requestAnimationFrame(stressTest);
          } else {
            log('✅ Stress test completed - 100 rapid updates performed', 'success');
            
            // Final performance assessment
            setTimeout(() => {
              if (performanceData.fps >= 55) {
                log(`🎉 EXCELLENT: Maintaining ${performanceData.fps} FPS`, 'success');
              } else if (performanceData.fps >= 30) {
                log(`⚠️ GOOD: Maintaining ${performanceData.fps} FPS`, 'warning');
              } else {
                log(`❌ POOR: Only ${performanceData.fps} FPS`, 'error');
              }
              
              if (performanceData.renderTime < 16.67) {
                log(`✅ Render time ${performanceData.renderTime.toFixed(2)}ms is excellent for 60fps`, 'success');
              } else {
                log(`⚠️ Render time ${performanceData.renderTime.toFixed(2)}ms may cause frame drops`, 'warning');
              }
            }, 2000);
          }
        }
        
        setTimeout(stressTest, 2000);

      } catch (error) {
        log(`❌ Performance test failed: ${error.message}`, 'error');
        console.error('Full error:', error);
      }
    }

    // Start tests
    runPerformanceTests();
  </script>
</body>
</html>
