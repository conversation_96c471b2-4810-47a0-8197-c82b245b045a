// GameDashboardScreen.js
import { renderGame, renderGameDOM } from '../game/renderer.js';
import { performanceEngine } from '../game/performanceEngine.js';
import { subscribeToState, sendAction, sendChat } from '../multiplayer/sync.js';
import { getState } from '../../msec/framework/state.js';
import { renderDOM } from '../../msec/framework/dom.js';
import { navigate } from '../../msec/framework/router.js';
import { gameEndScreen } from './gameEndScreen.js';

let root = null;
let gameState = null;
let inputHandlersAttached = false;
let performanceInitialized = false;

export function GameDashboardScreen() {
  root = document.getElementById('app');
  console.log('GameDashboardScreen initialized');

  const vdom = {
    tag: 'div',
    attrs: { class: 'character-container' },
    children: [
        {
            tag: 'div',
            attrs: { class: 'main-menu' },
            children: [{
                tag: 'div',
                attrs: { class: 'menu-container' },
                children: [
                    {
                        tag: 'div',
                        attrs: { class: 'left-border-shape' },
                        children: [{
                            tag: 'img',
                            attrs: {
                              src: './static/images/left-border-edge.png',
                              alt: 'Left Border Shape'
                            },
                            children: [],
                        }]
                    },
                    {
                        tag: 'div',
                        attrs: { class: 'menu-content' },
                        children: [
                            {
                                tag: 'div',
                                attrs: { class: 'left-side' },
                                children: [
                                    { tag: 'a', attrs: { href: 'index.html' }, children: ['Main Menu'] },
                                    { tag: 'div', attrs: { class: 'seperator' }, children: [] },
                                    { tag: 'p', attrs: { class: 'player-font' }, children: ['Player 1'] },
                                    {
                                        tag: 'div',
                                        attrs: { class: 'hearts' },
                                        children: [
                                            { tag: 'div', attrs: { class: 'heart' }, children: [] },
                                            { tag: 'div', attrs: { class: 'heart' }, children: [] },
                                            { tag: 'div', attrs: { class: 'heart' }, children: [] }
                                        ]
                                    }
                                ]
                            },
                            {
                                tag: 'div',
                                attrs: { class: 'right-side' },
                                children: [
                                    {
                                        tag: 'div',
                                        attrs: { class: 'stat' },
                                        children: [{
                                            tag: 'div',
                                            attrs: { class: 'flex' },
                                            children: [
                                                { tag: 'img', attrs: { src: './static/images/Speed.png', alt: 'Speed Icon' }, children: [] },
                                                { tag: 'h3', attrs: { class: 'speed-stat' }, children: ['3'] }
                                            ]
                                        }]
                                    },
                                    {
                                        tag: 'div',
                                        attrs: { class: 'stat' },
                                        children: [{
                                            tag: 'div',
                                            attrs: { class: 'flex' },
                                            children: [
                                                { tag: 'img', attrs: { src: './static/images/bomb.png', alt: 'Bomb Icon' }, children: [] },
                                                { tag: 'h3', attrs: { class: 'bomb-stat' }, children: ['1'] }
                                            ]
                                        }]
                                    },
                                    {
                                        tag: 'div',
                                        attrs: { class: 'stat' },
                                        children: [{
                                            tag: 'div',
                                            attrs: { class: 'flex' },
                                            children: [
                                                { tag: 'img', attrs: { src: './static/images/Fire.png', alt: 'Fire Icon' }, children: [] },
                                                { tag: 'h3', attrs: { class: 'fire-stat' }, children: ['3'] }
                                            ]
                                        }]
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        tag: 'div',
                        attrs: { class: 'right-border-shape' },
                        children: [{
                            tag: 'img',
                            attrs: {
                              src: './static/images/left-border-edge.png',
                              alt: 'Left Border Shape'
                            },
                            children: [],
                        }]
                    }
                ]
            }]
        },
        // dashboard
        {
          tag: 'div',
          attrs: { class: 'dashboard' },
          children: [
            //chat
            {
              tag: 'div',
              attrs: { class: 'chat-container closed' },
              children: [
                {
                  tag: 'div',
                  attrs: { class: 'chat-drawer' },
                  children: [
                    { tag: 'p', attrs: { class: 'player-font' }, children: ['Chat'] }
                  ]
                },
                {
                  tag: 'div',
                  attrs: { class: 'chat-sidebar' },
                  children: [
                    {
                      tag: 'div',
                      attrs: { class: 'chat-messages' },
                      children: [
                        {
                          tag: 'div',
                          attrs: { class: 'chat-container-inner' },
                          children: Array(11).fill().map(() => ({
                            tag: 'div',
                            attrs: { class: 'chat-message' },
                            children: [
                              { tag: 'span', attrs: { class: 'player-font' }, children: ['Player1:'] },
                              { tag: 'p', children: ['Hello, ready to play?'] }
                            ]
                          }))
                        }
                      ]
                    },
                    {
                      tag: 'div',
                      attrs: { class: 'input-form' },
                      children: [
                        {
                          tag: 'form',
                          attrs: { action: '#', method: 'post' },
                          children: [
                            {
                              tag: 'input',
                              attrs: {
                                type: 'text',
                                placeholder: 'Message',
                                class: 'chat-input'
                              }
                            },
                            {
                              tag: 'button',
                              attrs: { type: 'submit', class: 'send-button' },
                              children: [
                                {
                                  tag: 'img',
                                  attrs: {
                                    src: './static/images/send.png',
                                    alt: 'Send Icon'
                                  }
                                }
                              ]
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            },
            // game area
            {
              tag: 'div',
              attrs: { class: 'game-area' },
              children: [
                {
                  tag: 'div',
                  attrs: { class: 'game-board', id: 'game-board' },
                  children: [] // Game content will be rendered here
                }
              ]
            },
            // player stats sidebar
            {
              tag: 'div',
              attrs: { class: 'player-stats-sidebar' },
              children: Array(3).fill().map(() => ({
                tag: 'div',
                attrs: { class: 'player-stat' },
                children: [
                  {
                    tag: 'div',
                    attrs: { class: 'player-name' },
                    children: [
                      { tag: 'h2', attrs: { class: 'player-font' }, children: ['Player 2'] },
                      {
                        tag: 'div',
                        attrs: { class: 'hearts' },
                        children: Array(3).fill().map(() => ({
                          tag: 'div',
                          attrs: { class: 'heart' }
                        }))
                      }
                    ]
                  },
                  {
                    tag: 'div',
                    attrs: { class: 'player-stats' },
                    children: [
                      {
                        tag: 'div',
                        attrs: { class: 'stat' },
                        children: [
                          {
                            tag: 'div',
                            attrs: { class: 'flex' },
                            children: [
                              {
                                tag: 'img',
                                attrs: {
                                  src: './static/images/bomb.png',
                                  alt: 'Bomb Icon'
                                }
                              },
                              {
                                tag: 'h3',
                                attrs: { class: 'speed-stat' },
                                children: ['bomb: 1']
                              }
                            ]
                          }
                        ]
                      },
                      {
                        tag: 'div',
                        attrs: { class: 'stat' },
                        children: [
                          {
                            tag: 'div',
                            attrs: { class: 'flex' },
                            children: [
                              {
                                tag: 'img',
                                attrs: {
                                  src: './static/images/Fire.png',
                                  alt: 'Bomb Icon'
                                }
                              },
                              {
                                tag: 'h3',
                                attrs: { class: 'speed-stat' },
                                children: ['Flame: 3']
                              }
                            ]
                          }
                        ]
                      },
                      {
                        tag: 'div',
                        attrs: { class: 'stat' },
                        children: [
                          {
                            tag: 'div',
                            attrs: { class: 'flex' },
                            children: [
                              {
                                tag: 'img',
                                attrs: {
                                  src: './static/images/Speed.png',
                                  alt: 'Speed Icon'
                                }
                              },
                              {
                                tag: 'h3',
                                attrs: { class: 'speed-stat' },
                                children: ['Speed: 1']
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }))
            }
          ]
        },
        // player stats down
        {
          tag: 'div',
          attrs: { class: 'player-stats-down' },
          children: Array(3).fill().map(() => ({
            tag: 'div',
            attrs: { class: 'player-stat' },
            children: [
              {
                tag: 'div',
                attrs: { class: 'player-name' },
                children: [
                  { tag: 'h2', attrs: { class: 'player-font' }, children: ['Player 2'] },
                  {
                    tag: 'div',
                    attrs: { class: 'hearts' },
                    children: Array(3).fill().map(() => ({
                      tag: 'div',
                      attrs: { class: 'heart' },
                      children: [],
                    })),
                  },
                ],
              },
              {
                tag: 'div',
                attrs: { class: 'player-stats' },
                children: [
                  {
                    tag: 'div',
                    attrs: { class: 'stat' },
                    children: [
                      {
                        tag: 'div',
                        attrs: { class: 'flex' },
                        children: [
                          { tag: 'img', attrs: { src: './static/images/bomb.png', alt: 'Bomb Icon' }, children: [] },
                          { tag: 'h3', attrs: { class: 'speed-stat' }, children: ['bomb: 1'] },
                        ],
                      },
                    ],
                  },
                  {
                    tag: 'div',
                    attrs: { class: 'stat' },
                    children: [
                      {
                        tag: 'div',
                        attrs: { class: 'flex' },
                        children: [
                          { tag: 'img', attrs: { src: './static/images/Fire.png', alt: 'Bomb Icon' }, children: [] },
                          { tag: 'h3', attrs: { class: 'speed-stat' }, children: ['Flame: 3'] },
                        ],
                      },
                    ],
                  },
                  {
                    tag: 'div',
                    attrs: { class: 'stat' },
                    children: [
                      {
                        tag: 'div',
                        attrs: { class: 'flex' },
                        children: [
                          { tag: 'img', attrs: { src: './static/images/Speed.png', alt: 'Speed Icon' }, children: [] },
                          { tag: 'h3', attrs: { class: 'speed-stat' }, children: ['Speed: 1'] },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          })),
        },

    ]
  };

  function setupChatToggle() {
    const chatDrawer = document.querySelector('.chat-drawer');
    const chatContainer = document.querySelector('.chat-container');
    if (chatDrawer && chatContainer) {
      chatDrawer.addEventListener('click', () => {
        chatContainer.classList.toggle('closed');
      });
    }
  }

  function setupChatInput() {
    const chatForm = document.querySelector('.input-form form');
    const chatInput = document.querySelector('.chat-input');

    if (chatForm && chatInput) {
      chatForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const message = chatInput.value.trim();
        if (message) {
          sendChat(message);
          chatInput.value = '';
        }
      });
    }
  }

  function updatePlayerStats(players) {
    if (!players || !Array.isArray(players)) return;

    // Find the current player (the one with matching ID from localStorage)
    const playerId = localStorage.getItem('playerId');
    const currentPlayer = players.find(p => p.id === playerId) || players[0];

    if (currentPlayer) {
      const speedStat = document.querySelector('.left-side .speed-stat');
      const bombStat = document.querySelector('.left-side .bomb-stat');
      const fireStat = document.querySelector('.left-side .fire-stat');
      const playerName = document.querySelector('.left-side .player-font');
      const hearts = document.querySelectorAll('.left-side .heart');

      if (speedStat) speedStat.textContent = currentPlayer.speed || 1;
      if (bombStat) bombStat.textContent = currentPlayer.bombsCount || 1;
      if (fireStat) fireStat.textContent = currentPlayer.flameRange || 1;
      if (playerName) playerName.textContent = currentPlayer.nickname || 'Player';

      // Update hearts based on lives
      hearts.forEach((heart, index) => {
        if (index < (currentPlayer.lives || 0)) {
          heart.style.opacity = '1';
          heart.style.filter = 'none';
        } else {
          heart.style.opacity = '0.3';
          heart.style.filter = 'grayscale(100%)';
        }
      });
    }

    // Update other players' stats in the right side
    const otherPlayers = players.filter(p => p.id !== playerId).slice(0, 3);

    otherPlayers.forEach((player, index) => {
      const playerContainer = document.querySelector(`.right-side .player-container:nth-child(${index + 1})`);
      if (playerContainer) {
        const playerName = playerContainer.querySelector('.player-font');
        const hearts = playerContainer.querySelectorAll('.heart');

        if (playerName) playerName.textContent = player.nickname || `Player ${index + 2}`;

        hearts.forEach((heart, heartIndex) => {
          if (heartIndex < (player.lives || 0)) {
            heart.style.opacity = '1';
            heart.style.filter = 'none';
          } else {
            heart.style.opacity = '0.3';
            heart.style.filter = 'grayscale(100%)';
          }
        });

        // Add alive/dead indicator
        if (player.alive === false) {
          playerContainer.style.opacity = '0.5';
          playerContainer.style.filter = 'grayscale(50%)';
        } else {
          playerContainer.style.opacity = '1';
          playerContainer.style.filter = 'none';
        }
      }
    });
  }

  function updateChatMessages(chatMessages) {
    const chatContainer = document.querySelector('.chat-container-inner');
    if (!chatContainer || !chatMessages || !Array.isArray(chatMessages)) return;

    // Clear and rebuild chat messages
    chatContainer.innerHTML = '';

    // Show last 10 messages
    const recentMessages = chatMessages.slice(-10);
    recentMessages.forEach(msg => {
      const messageDiv = document.createElement('div');
      messageDiv.className = 'chat-message';

      const nicknameSpan = document.createElement('span');
      nicknameSpan.className = 'player-font';
      nicknameSpan.textContent = `${msg.nickname}:`;

      const textP = document.createElement('p');
      textP.textContent = msg.text;

      messageDiv.appendChild(nicknameSpan);
      messageDiv.appendChild(textP);
      chatContainer.appendChild(messageDiv);
    });

    // Auto-scroll to bottom
    chatContainer.scrollTop = chatContainer.scrollHeight;
  }

  function renderGameContent(state) {
    const gameBoard = document.getElementById('game-board');
    if (!gameBoard || !state) return;

    // Handle different game phases
    if (state.phase === 'lobby') {
      // Stop performance engine when leaving game
      if (performanceInitialized) {
        performanceEngine.stop();
        performanceInitialized = false;
      }
      navigate('/lobby');
      return;
    }

    if (state.phase === 'end') {
      // Stop performance engine when game ends
      if (performanceInitialized) {
        performanceEngine.stop();
        performanceInitialized = false;
      }
      gameEndScreen(root, state.winner, state.players || []);
      return;
    }

    if (state.phase === 'game' && state.map) {
      // Initialize performance engine for game rendering
      if (!performanceInitialized) {
        performanceEngine.init(gameBoard, (gameState, deltaTime) => {
          // Performance engine callback for 60fps updates
          // This is called at 60fps for smooth rendering
          if (gameState && gameBoard) {
            try {
              renderGame(gameState, gameBoard);
            } catch (error) {
              console.warn('[GameDashboard] Performance render failed:', error);
            }
          }
        });
        performanceEngine.start();
        performanceInitialized = true;
        console.log('[GameDashboard] Performance engine initialized for 60fps rendering');
      }

      // Update the performance engine with new state
      performanceEngine.updateState(state);

      // Clear previous content only once
      if (gameBoard.children.length === 0) {
        try {
          // Initial render
          renderGame(state, gameBoard);

          // Add controls info
          const controlsInfo = document.createElement('div');
          controlsInfo.style.cssText = 'margin-top: 10px; text-align: center; color: #ccc; font-size: 12px;';
          controlsInfo.innerHTML = 'Use WASD or Arrow Keys to move, Space to place bombs | P for performance stats';
          gameBoard.appendChild(controlsInfo);

          // Add performance stats display
          const perfStats = document.createElement('div');
          perfStats.id = 'perf-stats';
          perfStats.style.cssText = 'margin-top: 5px; text-align: center; color: #999; font-size: 10px;';
          gameBoard.appendChild(perfStats);

          // Update performance stats every second
          setInterval(() => {
            const stats = performanceEngine.getPerformanceMetrics();
            const perfStatsEl = document.getElementById('perf-stats');
            if (perfStatsEl && stats) {
              const color = stats.fps >= 55 ? '#0f0' : stats.fps >= 30 ? '#ff0' : '#f00';
              perfStatsEl.innerHTML = `<span style="color: ${color}">FPS: ${stats.fps}</span> | Frame Time: ${stats.avgFrameTime || 'N/A'}ms`;
            }
          }, 1000);

        } catch (error) {
          console.warn('[GameDashboard] Canvas rendering failed, using DOM fallback:', error);
          renderGameDOM(state, gameBoard);
        }
      }
    }
  }

  function attachInputHandlers() {
    if (inputHandlersAttached) return;

    const handleKeyDown = (e) => {
      // Don't handle input if typing in chat
      if (document.activeElement.tagName === 'INPUT' || document.activeElement.tagName === 'TEXTAREA') {
        return;
      }

      let action = null;
      switch (e.key) {
        case 'ArrowUp': case 'w': case 'W':
          action = { type: 'move', dir: 'up' };
          break;
        case 'ArrowDown': case 's': case 'S':
          action = { type: 'move', dir: 'down' };
          break;
        case 'ArrowLeft': case 'a': case 'A':
          action = { type: 'move', dir: 'left' };
          break;
        case 'ArrowRight': case 'd': case 'D':
          action = { type: 'move', dir: 'right' };
          break;
        case ' ':
          action = { type: 'bomb' };
          break;
        case 'p': case 'P':
          // Show performance stats
          if (performanceInitialized) {
            performanceEngine.logPerformanceStats();
          }
          e.preventDefault();
          return;
      }

      if (action) {
        console.log('[GameDashboard] Sending action:', action);
        sendAction(action);
        e.preventDefault();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    inputHandlersAttached = true;

    console.log('[GameDashboard] Input handlers attached');
  }

  // Subscribe to state updates from server
  subscribeToState((state) => {
    gameState = state;
    console.log('[GameDashboard] State update:', state);

    // Update UI components
    updatePlayerStats(state.players);
    updateChatMessages(state.chat);
    renderGameContent(state);
  });

  renderDOM(vdom, root);
  setupChatToggle();
  setupChatInput();
  attachInputHandlers();

  // Initial render with current state
  const initialState = getState();
  if (initialState) {
    gameState = initialState;
    updatePlayerStats(initialState.players);
    updateChatMessages(initialState.chat);
    renderGameContent(initialState);
  }

}
