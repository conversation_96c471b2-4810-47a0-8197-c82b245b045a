// gameScreen.js
import { renderGame, renderGameDOM } from '../game/renderer.js';
import { performanceEngine } from '../game/performanceEngine.js';
import { subscribeToState, sendAction } from '../multiplayer/sync.js';
import { getState } from '../../msec/framework/state.js';
import { chatUI } from './chat.js';
import { gameEndScreen } from './gameEndScreen.js';
import { navigate } from '../../msec/framework/router.js';

let root = null;

export function gameScreen() {
  root = document.getElementById('app');

  // Clear root and create static containers ONCE
  root.innerHTML = '';
  const gameRoot = document.createElement('div');
  gameRoot.id = 'game-root';
  gameRoot.style.cssText = 'margin-bottom: 20px;';
  root.appendChild(gameRoot);

  const chatRoot = document.createElement('div');
  chatRoot.id = 'chat-root';
  root.appendChild(chatRoot);

  // Initialize performance engine
  performanceEngine.init(gameRoot, onGameUpdate);
  performanceEngine.start();

  // Create chat UI ONCE
  try {
    chatUI(chatRoot);
  } catch (error) {
    console.warn('[GameScreen] Chat UI failed to load:', error);
    chatRoot.innerHTML = '<div style="color: #999; font-style: italic;">Chat unavailable</div>';
  }

  function render(state) {
    console.log('[GameScreen] Rendering with state:', state);

    // Handle game end state
    if (state && state.phase === 'end') {
      console.log('[GameScreen] Game ended, showing end screen');
      gameEndScreen(root, state.winner, state.players || []);
      return;
    }

    // Handle lobby state - redirect to lobby
    if (state && state.phase === 'lobby') {
      console.log('[GameScreen] Game returned to lobby, redirecting');
      navigate('/lobby');
      return;
    }

    // Only update gameRoot, not chatRoot
    if (!state || !Array.isArray(state.map)) {
      gameRoot.innerHTML = `
        <div class="game-loading" style="
          background: #333;
          padding: 40px;
          border-radius: 8px;
          text-align: center;
          color: #fff;
        ">
          <h2>🎮 Bomberman Game</h2>
          <p>Waiting for game to start...</p>
          <div style="margin-top: 20px;">
            <p><strong>Current State:</strong></p>
            <div style="font-family: monospace; background: #222; padding: 10px; border-radius: 4px; margin-top: 10px;">
              Players: ${state?.players?.length || 0}<br>
              Phase: ${state?.phase || 'unknown'}<br>
              Map: ${state?.map ? 'loaded' : 'not loaded'}
            </div>
          </div>
        </div>
      `;
      return;
    }

    // Clear and re-render game only
    gameRoot.innerHTML = '';

    // Create enhanced HUD
    const hudContainer = document.createElement('div');
    hudContainer.style.cssText = `
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 10px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-family: 'EngraversGothic', Arial, sans-serif;
      font-size: 14px;
      border-radius: 8px;
      margin-bottom: 10px;
    `;

    // Find current player
    const currentPlayer = (state.players || []).find(p => p.id === localStorage.getItem('playerId'));

    // Left side - Player stats
    const playerStats = document.createElement('div');
    if (currentPlayer) {
      playerStats.innerHTML = `
        <div style="display: flex; gap: 20px; align-items: center;">
          <span style="color: #F6265A; font-weight: bold;">${currentPlayer.nickname}</span>
          <span>Lives: ${'❤️'.repeat(currentPlayer.lives)}</span>
          <span>Bombs: ${currentPlayer.bombsCount}</span>
          <span>Range: ${currentPlayer.flameRange}</span>
          <span>Speed: ${currentPlayer.speed}</span>
        </div>
      `;
    } else {
      playerStats.innerHTML = '<span>Spectating</span>';
    }

    // Right side - Performance and game info
    const gameInfo = document.createElement('div');
    const metrics = performanceEngine.getPerformanceMetrics();
    gameInfo.innerHTML = `
      <div style="display: flex; gap: 15px; align-items: center;">
        <span>Players: ${(state.players || []).length}/4</span>
        <span>FPS: <span style="color: ${metrics.fps >= 55 ? '#0f0' : metrics.fps >= 30 ? '#ff0' : '#f00'}">${metrics.fps || 0}</span></span>
      </div>
    `;

    hudContainer.appendChild(playerStats);
    hudContainer.appendChild(gameInfo);
    gameRoot.appendChild(hudContainer);

    // Try canvas rendering first, fall back to DOM rendering
    try {
      const canvas = document.createElement('canvas');
      canvas.style.cssText = `
        border: 2px solid #666;
        display: block;
        margin: 0 auto;
        image-rendering: pixelated;
        image-rendering: -moz-crisp-edges;
        image-rendering: crisp-edges;
      `;
      gameRoot.appendChild(canvas);

      renderGame({
        map: state.map,
        players: state.players || [],
        bombs: state.bombs || [],
        explosions: state.explosions || [],
        powerups: state.powerups || [],
      }, canvas);

      // Add controls info
      const controlsInfo = document.createElement('div');
      controlsInfo.style.cssText = 'margin-top: 10px; text-align: center; color: #ccc; font-size: 12px;';
      controlsInfo.innerHTML = `
        <div>Use WASD or Arrow Keys to move, Space to place bombs, P for performance debug</div>
      `;
      gameRoot.appendChild(controlsInfo);

    } catch (error) {
      console.warn('[GameScreen] Canvas rendering failed, using DOM fallback:', error);
      renderGameDOM({
        map: state.map,
        players: state.players || [],
        bombs: state.bombs || [],
        explosions: state.explosions || [],
        powerups: state.powerups || [],
      }, gameRoot);
    }
  }

  // Game update callback for performance engine
  function onGameUpdate(state, deltaTime) {
    // This is called by the performance engine at 60fps
    // Can be used for client-side interpolation or effects
    // For now, we just update the performance engine state
    performanceEngine.updateState(state);
  }

  // Subscribe to state updates from server
  subscribeToState((state) => {
    performanceEngine.updateState(state);
    render(state);
  });

  // Initial render
  const initialState = getState();
  if (initialState) {
    performanceEngine.updateState(initialState);
    render(initialState);
  }

  // Handle player input
  window.onkeydown = (e) => {
    if (document.activeElement.tagName === 'INPUT') return; // Don't move if typing in chat
    let action = null;
    switch (e.key) {
      case 'ArrowUp': case 'w': case 'W': action = { type: 'move', dir: 'up' }; break;
      case 'ArrowDown': case 's': case 'S': action = { type: 'move', dir: 'down' }; break;
      case 'ArrowLeft': case 'a': case 'A': action = { type: 'move', dir: 'left' }; break;
      case 'ArrowRight': case 'd': case 'D': action = { type: 'move', dir: 'right' }; break;
      case ' ': action = { type: 'bomb' }; break;
    }
    if (action) {
      console.log('[GameScreen] Sending action:', action);
      sendAction(action);
      e.preventDefault();
    }
  };
}
