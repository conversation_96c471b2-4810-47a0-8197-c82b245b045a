// renderer.js
// Game rendering logic for Bomberman with 60fps performance

const TILE_SIZE = 32;
const MAP_WIDTH = 17;
const MAP_HEIGHT = 15;

// Performance tracking
let frameCount = 0;
let lastFpsUpdate = 0;
let currentFps = 0;

// Create and setup canvas for optimal performance
export function createGameCanvas(container) {
  const canvas = document.createElement('canvas');
  canvas.width = MAP_WIDTH * TILE_SIZE;
  canvas.height = MAP_HEIGHT * TILE_SIZE;
  canvas.style.cssText = `
    border: 2px solid #666;
    display: block;
    margin: 0 auto;
    background: #90EE90;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
  `;

  container.appendChild(canvas);
  return canvas;
}

// Main render function optimized for 60fps
export function renderGame(state, container) {
  if (!container || !state) {
    console.warn('[Renderer] Missing container or state');
    return;
  }

  // Create canvas if it doesn't exist
  let canvas = container.querySelector('canvas');
  if (!canvas) {
    canvas = createGameCanvas(container);
  }

  const ctx = canvas.getContext('2d');
  if (!ctx) {
    console.error('[Renderer] Could not get canvas context');
    return;
  }

  // Performance measurement
  const now = performance.now();
  frameCount++;
  if (now - lastFpsUpdate >= 1000) {
    currentFps = Math.round((frameCount * 1000) / (now - lastFpsUpdate));
    frameCount = 0;
    lastFpsUpdate = now;
  }

  // Clear canvas efficiently
  ctx.clearRect(0, 0, canvas.width, canvas.height);
  const map = state.map || [];
  if (map.length === 0) {
    // No map data, show loading
    ctx.fillStyle = '#333';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.fillStyle = '#fff';
    ctx.font = '20px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Loading game...', canvas.width / 2, canvas.height / 2);
    return;
  }

  // Render all game elements
  renderMap(ctx, map, TILE_SIZE);
  renderGameObjects(ctx, state, TILE_SIZE);

  // Render FPS counter for performance monitoring
  renderFpsCounter(ctx, currentFps);
}

// Optimized game objects rendering
function renderGameObjects(ctx, state, tileSize) {
  // Render powerups first (lowest layer)
  if (state.powerups && Array.isArray(state.powerups)) {
    state.powerups.forEach(powerup => {
      if (!powerup.collected) {
        renderPowerup(ctx, powerup, tileSize);
      }
    });
  }

  // Render bombs
  if (state.bombs && Array.isArray(state.bombs)) {
    state.bombs.forEach(bomb => {
      if (!bomb.exploded) {
        renderBomb(ctx, bomb, tileSize);
      }
    });
  }

  // Render explosions
  if (state.explosions && Array.isArray(state.explosions)) {
    state.explosions.forEach(explosion => {
      renderExplosion(ctx, explosion, tileSize);
    });
  }

  // Render players (highest layer)
  if (state.players && Array.isArray(state.players)) {
    state.players.forEach(player => {
      if (player.alive) {
        renderPlayer(ctx, player, tileSize);
      }
    });
  }
}

function renderMap(ctx, map, tileSize) {
  for (let y = 0; y < map.length; y++) {
    for (let x = 0; x < map[y].length; x++) {
      const tile = map[y][x];
      const pixelX = x * tileSize;
      const pixelY = y * tileSize;

      // Handle both string and numeric tile values
      switch (tile) {
        case 'E': // Empty (server format)
        case 0:   // Empty (client format)
          ctx.fillStyle = '#90EE90'; // Light green
          break;
        case 'W': // Wall (server format)
        case 1:   // Wall (client format)
          ctx.fillStyle = '#8B4513'; // Brown
          break;
        case 'B': // Destructible block (server format)
        case 2:   // Destructible (client format)
          ctx.fillStyle = '#DEB887'; // Burlywood
          break;
        default:
          ctx.fillStyle = '#90EE90'; // Default to empty
      }

      ctx.fillRect(pixelX, pixelY, tileSize, tileSize);

      // Add border for visibility
      ctx.strokeStyle = '#666';
      ctx.lineWidth = 1;
      ctx.strokeRect(pixelX, pixelY, tileSize, tileSize);
    }
  }
}

function renderPlayer(ctx, player, tileSize) {
  if (!player || typeof player.row !== 'number' || typeof player.col !== 'number') {
    return;
  }

  const pixelX = player.col * tileSize;
  const pixelY = player.row * tileSize;

  // Player colors based on ID or index
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];
  const colorIndex = player.id ? player.id.charCodeAt(0) % colors.length : 0;

  ctx.fillStyle = colors[colorIndex];
  ctx.fillRect(pixelX + 4, pixelY + 4, tileSize - 8, tileSize - 8);

  // Player border
  ctx.strokeStyle = '#000';
  ctx.lineWidth = 2;
  ctx.strokeRect(pixelX + 4, pixelY + 4, tileSize - 8, tileSize - 8);

  // Player nickname
  if (player.nickname) {
    ctx.fillStyle = '#000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(player.nickname, pixelX + tileSize / 2, pixelY - 5);
  }
}

function renderBomb(ctx, bomb, tileSize) {
  if (!bomb || typeof bomb.row !== 'number' || typeof bomb.col !== 'number') {
    return;
  }

  const pixelX = bomb.col * tileSize;
  const pixelY = bomb.row * tileSize;

  // Bomb animation based on timer
  const pulseIntensity = Math.sin(Date.now() / 200) * 0.3 + 0.7;

  ctx.fillStyle = `rgba(255, 0, 0, ${pulseIntensity})`;
  ctx.fillRect(pixelX + 6, pixelY + 6, tileSize - 12, tileSize - 12);

  // Bomb border
  ctx.strokeStyle = '#800';
  ctx.lineWidth = 2;
  ctx.strokeRect(pixelX + 6, pixelY + 6, tileSize - 12, tileSize - 12);

  // Timer display (simplified since server doesn't send timer)
  ctx.fillStyle = '#fff';
  ctx.font = '14px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('💣', pixelX + tileSize / 2, pixelY + tileSize / 2 + 5);
}

function renderExplosion(ctx, explosion, tileSize) {
  if (!explosion || typeof explosion.row !== 'number' || typeof explosion.col !== 'number') {
    return;
  }

  const pixelX = explosion.col * tileSize;
  const pixelY = explosion.row * tileSize;

  // Enhanced explosion animation with multiple layers
  const time = Date.now() / 100;
  const intensity1 = Math.sin(time * 2) * 0.3 + 0.7;
  const intensity2 = Math.sin(time * 3) * 0.2 + 0.8;

  // Outer explosion ring
  ctx.fillStyle = `rgba(255, 100, 0, ${intensity1})`;
  ctx.fillRect(pixelX, pixelY, tileSize, tileSize);

  // Inner explosion core
  ctx.fillStyle = `rgba(255, 200, 0, ${intensity2})`;
  ctx.fillRect(pixelX + 6, pixelY + 6, tileSize - 12, tileSize - 12);

  // Bright center
  ctx.fillStyle = `rgba(255, 255, 255, ${intensity1 * 0.8})`;
  ctx.fillRect(pixelX + 12, pixelY + 12, tileSize - 24, tileSize - 24);

  // Add some sparkle effects
  for (let i = 0; i < 3; i++) {
    const sparkleX = pixelX + Math.sin(time + i) * 8 + tileSize / 2;
    const sparkleY = pixelY + Math.cos(time + i) * 8 + tileSize / 2;
    ctx.fillStyle = `rgba(255, 255, 255, ${Math.random() * 0.8})`;
    ctx.fillRect(sparkleX - 1, sparkleY - 1, 2, 2);
  }
}

function renderPowerup(ctx, powerup, tileSize) {
  if (!powerup || typeof powerup.row !== 'number' || typeof powerup.col !== 'number') {
    return;
  }

  const pixelX = powerup.col * tileSize;
  const pixelY = powerup.row * tileSize;

  // Enhanced powerup rendering with pulsing effect
  const time = Date.now() / 500;
  const pulse = Math.sin(time) * 0.2 + 0.8;
  const glow = Math.sin(time * 2) * 0.3 + 0.7;

  // Powerup colors and symbols based on type
  let color = '#FFD700';
  let symbol = '?';
  let glowColor = '#FFD700';

  switch (powerup.type) {
    case 'speed_up':
      color = '#00FF00';
      glowColor = '#00FF00';
      symbol = 'S';
      break;
    case 'bomb_up':
      color = '#FF0000';
      glowColor = '#FF0000';
      symbol = 'B';
      break;
    case 'flame_up':
      color = '#0000FF';
      glowColor = '#0000FF';
      symbol = 'F';
      break;
  }

  // Glow effect
  ctx.shadowColor = glowColor;
  ctx.shadowBlur = 10 * glow;

  // Main powerup body with pulsing
  const size = (tileSize - 16) * pulse;
  const offset = (tileSize - size) / 2;

  ctx.fillStyle = color;
  ctx.fillRect(pixelX + offset, pixelY + offset, size, size);

  // Reset shadow
  ctx.shadowBlur = 0;

  // Powerup border
  ctx.strokeStyle = '#000';
  ctx.lineWidth = 2;
  ctx.strokeRect(pixelX + offset, pixelY + offset, size, size);

  // Powerup symbol with enhanced styling
  ctx.fillStyle = '#FFF';
  ctx.font = 'bold 14px Arial';
  ctx.textAlign = 'center';
  ctx.strokeStyle = '#000';
  ctx.lineWidth = 1;

  // Draw text with outline
  ctx.strokeText(symbol, pixelX + tileSize / 2, pixelY + tileSize / 2 + 4);
  ctx.fillText(symbol, pixelX + tileSize / 2, pixelY + tileSize / 2 + 4);
}

// FPS counter for performance monitoring
function renderFpsCounter(ctx, fps) {
  ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
  ctx.fillRect(10, 10, 80, 30);

  ctx.fillStyle = fps >= 55 ? '#0f0' : fps >= 30 ? '#ff0' : '#f00';
  ctx.font = '14px Arial';
  ctx.textAlign = 'left';
  ctx.fillText(`FPS: ${fps}`, 15, 30);
}

// Create a simple fallback renderer for when canvas is not available
export function renderGameDOM(state, container) {
  if (!container || !state) {
    console.warn('[Renderer] Missing container or state');
    return;
  }

  container.innerHTML = '';

  // Create a simple text-based representation
  const gameInfo = document.createElement('div');
  gameInfo.style.cssText = 'font-family: monospace; background: #333; color: #fff; padding: 20px; border-radius: 8px;';

  let html = '<h3>🎮 Bomberman Game State</h3>';

  if (state.players && state.players.length > 0) {
    html += '<h4>👥 Players:</h4>';
    state.players.forEach(player => {
      // Handle both pixel coordinates (x,y) and grid coordinates (row,col)
      const x = player.x !== undefined ? Math.floor(player.x/32) : player.col || 0;
      const y = player.y !== undefined ? Math.floor(player.y/32) : player.row || 0;
      html += `<div>• ${player.nickname} at (${x}, ${y})</div>`;
    });
  } else {
    html += '<div>No players in game</div>';
  }

  if (state.bombs && state.bombs.length > 0) {
    html += '<h4>💣 Bombs:</h4>';
    state.bombs.forEach(bomb => {
      // Handle both pixel coordinates (x,y) and grid coordinates (row,col)
      const x = bomb.x !== undefined ? Math.floor(bomb.x/32) : bomb.col || 0;
      const y = bomb.y !== undefined ? Math.floor(bomb.y/32) : bomb.row || 0;
      const timer = bomb.timer ? Math.ceil(bomb.timer/1000) : '?';
      html += `<div>• Bomb at (${x}, ${y}) - ${timer}s</div>`;
    });
  }

  if (state.explosions && state.explosions.length > 0) {
    html += '<h4>💥 Explosions:</h4>';
    state.explosions.forEach(explosion => {
      const x = explosion.x !== undefined ? Math.floor(explosion.x/32) : explosion.col || 0;
      const y = explosion.y !== undefined ? Math.floor(explosion.y/32) : explosion.row || 0;
      html += `<div>• Explosion at (${x}, ${y})</div>`;
    });
  }

  if (state.powerups && state.powerups.length > 0) {
    html += '<h4>⚡ Powerups:</h4>';
    state.powerups.forEach(powerup => {
      const x = powerup.x !== undefined ? Math.floor(powerup.x/32) : powerup.col || 0;
      const y = powerup.y !== undefined ? Math.floor(powerup.y/32) : powerup.row || 0;
      html += `<div>• ${powerup.type} at (${x}, ${y})</div>`;
    });
  }

  if (state.map && state.map.length > 0) {
    html += `<h4>🗺️ Map: ${state.map[0].length}x${state.map.length}</h4>`;
    html += '<div style="font-size: 10px; line-height: 1.2; margin-top: 10px;">';
    state.map.forEach((row, y) => {
      let rowStr = '';
      row.forEach((tile, x) => {
        switch (tile) {
          case 'E': case 0: rowStr += '·'; break; // Empty
          case 'W': case 1: rowStr += '█'; break; // Wall
          case 'B': case 2: rowStr += '▓'; break; // Destructible
          default: rowStr += '?'; break;
        }
      });
      html += `<div>${rowStr}</div>`;
    });
    html += '</div>';
  }

  html += '<div style="margin-top: 20px; font-size: 12px; color: #ccc;">Canvas rendering not available - showing text representation</div>';

  gameInfo.innerHTML = html;
  container.appendChild(gameInfo);
}
