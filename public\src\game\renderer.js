// renderer.js
// DOM-based game rendering logic for Bomberman with 60fps performance

const TILE_SIZE = 32;
const MAP_WIDTH = 17;
const MAP_HEIGHT = 15;

// Performance tracking
let frameCount = 0;
let lastFpsUpdate = 0;
let currentFps = 0;

// Cache for DOM elements to avoid recreating them
let gameBoard = null;
let tileElements = new Map();
let gameObjectElements = new Map();

// Main render function optimized for 60fps DOM rendering
export function renderGame(state, container) {
  if (!container || !state) {
    console.warn('[Renderer] Missing container or state');
    return;
  }

  // Performance measurement
  const now = performance.now();
  frameCount++;
  if (now - lastFpsUpdate >= 1000) {
    currentFps = Math.round((frameCount * 1000) / (now - lastFpsUpdate));
    frameCount = 0;
    lastFpsUpdate = now;
  }

  // Create or get game board
  if (!gameBoard || !container.contains(gameBoard)) {
    gameBoard = createGameBoard(container);
  }

  const map = state.map || [];
  if (map.length === 0) {
    // No map data, show loading
    showLoadingState(gameBoard);
    return;
  }

  // Render all game elements
  renderMapDOM(gameBoard, map);
  renderGameObjectsDOM(gameBoard, state);

  // Render FPS counter for performance monitoring
  renderFpsCounterDOM(gameBoard, currentFps);
}

// Create the main game board container
function createGameBoard(container) {
  // Clear container
  container.innerHTML = '';

  const board = document.createElement('div');
  board.className = 'game-board';
  board.style.cssText = `
    position: relative;
    width: ${MAP_WIDTH * TILE_SIZE}px;
    height: ${MAP_HEIGHT * TILE_SIZE}px;
    margin: 0 auto;
    border: 2px solid #666;
    background: #90EE90;
    overflow: hidden;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
  `;

  container.appendChild(board);

  // Clear caches when creating new board
  tileElements.clear();
  gameObjectElements.clear();

  return board;
}

// Show loading state
function showLoadingState(board) {
  board.innerHTML = `
    <div style="
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: #fff;
      font-size: 20px;
      font-family: Arial, sans-serif;
      text-align: center;
      background: rgba(0, 0, 0, 0.8);
      padding: 20px;
      border-radius: 8px;
    ">
      Loading game...
    </div>
  `;
}

// DOM-based map rendering
function renderMapDOM(board, map) {
  for (let y = 0; y < map.length; y++) {
    for (let x = 0; x < map[y].length; x++) {
      const tile = map[y][x];
      const tileKey = `tile-${x}-${y}`;

      let tileElement = tileElements.get(tileKey);
      if (!tileElement) {
        tileElement = document.createElement('div');
        tileElement.className = 'game-tile';
        tileElement.style.cssText = `
          position: absolute;
          left: ${x * TILE_SIZE}px;
          top: ${y * TILE_SIZE}px;
          width: ${TILE_SIZE}px;
          height: ${TILE_SIZE}px;
          border: 1px solid #666;
          box-sizing: border-box;
        `;
        board.appendChild(tileElement);
        tileElements.set(tileKey, tileElement);
      }

      // Update tile appearance based on type
      let backgroundColor;
      switch (tile) {
        case 'E': // Empty (server format)
        case 0:   // Empty (client format)
          backgroundColor = 'transparent'; // Use board background
          break;
        case 'W': // Wall (server format)
        case 1:   // Wall (client format)
          backgroundColor = '#8B4513'; // Brown
          break;
        case 'B': // Destructible block (server format)
        case 2:   // Destructible (client format)
          backgroundColor = '#DEB887'; // Burlywood
          break;
        default:
          backgroundColor = 'transparent'; // Default to empty
      }

      tileElement.style.backgroundColor = backgroundColor;
    }
  }
}

// DOM-based game objects rendering
function renderGameObjectsDOM(board, state) {
  // Clear existing game objects (keep tiles)
  const existingObjects = board.querySelectorAll('.game-object');
  existingObjects.forEach(obj => obj.remove());
  gameObjectElements.clear();

  // Render powerups first (lowest layer)
  if (state.powerups && Array.isArray(state.powerups)) {
    state.powerups.forEach((powerup, index) => {
      if (!powerup.collected) {
        renderPowerupDOM(board, powerup, index);
      }
    });
  }

  // Render bombs
  if (state.bombs && Array.isArray(state.bombs)) {
    state.bombs.forEach((bomb, index) => {
      if (!bomb.exploded) {
        renderBombDOM(board, bomb, index);
      }
    });
  }

  // Render explosions
  if (state.explosions && Array.isArray(state.explosions)) {
    state.explosions.forEach((explosion, index) => {
      renderExplosionDOM(board, explosion, index);
    });
  }

  // Render players (highest layer)
  if (state.players && Array.isArray(state.players)) {
    state.players.forEach((player, index) => {
      if (player.alive) {
        renderPlayerDOM(board, player, index);
      }
    });
  }
}

// DOM-based player rendering
function renderPlayerDOM(board, player, index) {
  if (!player || typeof player.row !== 'number' || typeof player.col !== 'number') {
    return;
  }

  const pixelX = player.col * TILE_SIZE;
  const pixelY = player.row * TILE_SIZE;

  // Player colors based on ID or index
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'];
  const colorIndex = player.id ? player.id.charCodeAt(0) % colors.length : 0;

  const playerElement = document.createElement('div');
  playerElement.className = 'game-object player';
  playerElement.style.cssText = `
    position: absolute;
    left: ${pixelX + 4}px;
    top: ${pixelY + 4}px;
    width: ${TILE_SIZE - 8}px;
    height: ${TILE_SIZE - 8}px;
    background-color: ${colors[colorIndex]};
    border: 2px solid #000;
    box-sizing: border-box;
    z-index: 100;
  `;

  board.appendChild(playerElement);

  // Player nickname
  if (player.nickname) {
    const nicknameElement = document.createElement('div');
    nicknameElement.className = 'player-nickname';
    nicknameElement.textContent = player.nickname;
    nicknameElement.style.cssText = `
      position: absolute;
      left: ${pixelX + TILE_SIZE / 2}px;
      top: ${pixelY - 20}px;
      transform: translateX(-50%);
      color: #000;
      font-size: 12px;
      font-family: Arial, sans-serif;
      font-weight: bold;
      text-shadow: 1px 1px 1px rgba(255, 255, 255, 0.8);
      z-index: 101;
      pointer-events: none;
    `;
    board.appendChild(nicknameElement);
  }
}

// DOM-based bomb rendering
function renderBombDOM(board, bomb, index) {
  if (!bomb || typeof bomb.row !== 'number' || typeof bomb.col !== 'number') {
    return;
  }

  const pixelX = bomb.col * TILE_SIZE;
  const pixelY = bomb.row * TILE_SIZE;

  const bombElement = document.createElement('div');
  bombElement.className = 'game-object bomb';
  bombElement.style.cssText = `
    position: absolute;
    left: ${pixelX + 6}px;
    top: ${pixelY + 6}px;
    width: ${TILE_SIZE - 12}px;
    height: ${TILE_SIZE - 12}px;
    background-color: #ff0000;
    border: 2px solid #800;
    box-sizing: border-box;
    z-index: 50;
    animation: bomb-pulse 0.4s ease-in-out infinite alternate;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
  `;

  bombElement.textContent = '💣';
  board.appendChild(bombElement);
}

// DOM-based explosion rendering
function renderExplosionDOM(board, explosion, index) {
  if (!explosion || typeof explosion.row !== 'number' || typeof explosion.col !== 'number') {
    return;
  }

  const pixelX = explosion.col * TILE_SIZE;
  const pixelY = explosion.row * TILE_SIZE;

  // Outer explosion ring
  const outerExplosion = document.createElement('div');
  outerExplosion.className = 'game-object explosion-outer';
  outerExplosion.style.cssText = `
    position: absolute;
    left: ${pixelX}px;
    top: ${pixelY}px;
    width: ${TILE_SIZE}px;
    height: ${TILE_SIZE}px;
    background-color: rgba(255, 100, 0, 0.8);
    z-index: 75;
    animation: explosion-pulse 0.2s ease-in-out infinite alternate;
  `;
  board.appendChild(outerExplosion);

  // Inner explosion core
  const innerExplosion = document.createElement('div');
  innerExplosion.className = 'game-object explosion-inner';
  innerExplosion.style.cssText = `
    position: absolute;
    left: ${pixelX + 6}px;
    top: ${pixelY + 6}px;
    width: ${TILE_SIZE - 12}px;
    height: ${TILE_SIZE - 12}px;
    background-color: rgba(255, 200, 0, 0.9);
    z-index: 76;
    animation: explosion-pulse 0.15s ease-in-out infinite alternate;
  `;
  board.appendChild(innerExplosion);

  // Bright center
  const centerExplosion = document.createElement('div');
  centerExplosion.className = 'game-object explosion-center';
  centerExplosion.style.cssText = `
    position: absolute;
    left: ${pixelX + 12}px;
    top: ${pixelY + 12}px;
    width: ${TILE_SIZE - 24}px;
    height: ${TILE_SIZE - 24}px;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 77;
    animation: explosion-pulse 0.1s ease-in-out infinite alternate;
  `;
  board.appendChild(centerExplosion);
}

// DOM-based powerup rendering
function renderPowerupDOM(board, powerup, index) {
  if (!powerup || typeof powerup.row !== 'number' || typeof powerup.col !== 'number') {
    return;
  }

  const pixelX = powerup.col * TILE_SIZE;
  const pixelY = powerup.row * TILE_SIZE;

  // Powerup colors and symbols based on type
  let color = '#FFD700';
  let symbol = '?';

  switch (powerup.type) {
    case 'speed_up':
      color = '#00FF00';
      symbol = 'S';
      break;
    case 'bomb_up':
      color = '#FF0000';
      symbol = 'B';
      break;
    case 'flame_up':
      color = '#0000FF';
      symbol = 'F';
      break;
  }

  const powerupElement = document.createElement('div');
  powerupElement.className = 'game-object powerup';
  powerupElement.style.cssText = `
    position: absolute;
    left: ${pixelX + 8}px;
    top: ${pixelY + 8}px;
    width: ${TILE_SIZE - 16}px;
    height: ${TILE_SIZE - 16}px;
    background-color: ${color};
    border: 2px solid #000;
    box-sizing: border-box;
    z-index: 25;
    animation: powerup-pulse 1s ease-in-out infinite alternate;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    color: #fff;
    text-shadow: 1px 1px 1px #000;
    box-shadow: 0 0 10px ${color};
  `;

  powerupElement.textContent = symbol;
  board.appendChild(powerupElement);
}

// DOM-based FPS counter for performance monitoring
function renderFpsCounterDOM(board, fps) {
  let fpsCounter = board.querySelector('.fps-counter');
  if (!fpsCounter) {
    fpsCounter = document.createElement('div');
    fpsCounter.className = 'fps-counter';
    fpsCounter.style.cssText = `
      position: absolute;
      top: 10px;
      left: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: ${fps >= 55 ? '#0f0' : fps >= 30 ? '#ff0' : '#f00'};
      padding: 5px 10px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      border-radius: 4px;
      z-index: 200;
    `;
    board.appendChild(fpsCounter);
  }

  fpsCounter.textContent = `FPS: ${fps}`;
  fpsCounter.style.color = fps >= 55 ? '#0f0' : fps >= 30 ? '#ff0' : '#f00';
}

// Export renderGameDOM as an alias to renderGame for backward compatibility
export const renderGameDOM = renderGame;
