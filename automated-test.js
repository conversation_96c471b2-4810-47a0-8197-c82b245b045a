// Automated Bomberman Test Script
// Run this in Node.js to test the WebSocket server functionality

const WebSocket = require('./server/node_modules/ws');

class BombermanTester {
    constructor() {
        this.players = [];
        this.testResults = [];
        this.serverUrl = 'ws://localhost:8080';
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`[${timestamp}] ${prefix} ${message}`);
    }

    async createPlayer(nickname) {
        return new Promise((resolve, reject) => {
            const ws = new WebSocket(this.serverUrl);
            const player = {
                nickname,
                ws,
                id: null,
                connected: false,
                messages: []
            };

            ws.on('open', () => {
                this.log(`Player ${nickname} connected to server`, 'success');
                player.connected = true;
                
                // Send nickname to join game
                ws.send(JSON.stringify({
                    type: 'nickname',
                    nickname: nickname
                }));
            });

            ws.on('message', (data) => {
                try {
                    const msg = JSON.parse(data);
                    player.messages.push(msg);
                    
                    if (msg.type === 'playerId') {
                        player.id = msg.id;
                        this.log(`Player ${nickname} received ID: ${msg.id}`);
                    }
                    
                    if (msg.type === 'state') {
                        this.log(`Player ${nickname} received state update: phase=${msg.state.phase}, players=${msg.state.players?.length || 0}`);
                    }
                } catch (error) {
                    this.log(`Player ${nickname} received invalid JSON: ${data}`, 'error');
                }
            });

            ws.on('error', (error) => {
                this.log(`Player ${nickname} WebSocket error: ${error.message}`, 'error');
                reject(error);
            });

            ws.on('close', () => {
                this.log(`Player ${nickname} disconnected`);
                player.connected = false;
            });

            // Resolve after connection is established
            setTimeout(() => {
                if (player.connected) {
                    resolve(player);
                } else {
                    reject(new Error(`Player ${nickname} failed to connect`));
                }
            }, 1000);
        });
    }

    async testBasicConnection() {
        this.log('🔗 Testing basic WebSocket connection...');
        
        try {
            const player1 = await this.createPlayer('TestPlayer1');
            this.players.push(player1);
            
            // Wait for initial state
            await this.wait(2000);
            
            if (player1.id && player1.messages.length > 0) {
                this.log('Basic connection test PASSED', 'success');
                return true;
            } else {
                this.log('Basic connection test FAILED - No player ID or messages received', 'error');
                return false;
            }
        } catch (error) {
            this.log(`Basic connection test FAILED: ${error.message}`, 'error');
            return false;
        }
    }

    async testMultiplayerLobby() {
        this.log('👥 Testing multiplayer lobby functionality...');
        
        try {
            // Create 3 more players (we already have 1)
            const player2 = await this.createPlayer('TestPlayer2');
            const player3 = await this.createPlayer('TestPlayer3');
            const player4 = await this.createPlayer('TestPlayer4');
            
            this.players.push(player2, player3, player4);
            
            // Wait for lobby state updates
            await this.wait(3000);
            
            // Check if all players received state updates with correct player count
            let allPlayersInLobby = true;
            for (const player of this.players) {
                const lastState = player.messages.filter(m => m.type === 'state').pop();
                if (!lastState || !lastState.state.players || lastState.state.players.length !== 4) {
                    allPlayersInLobby = false;
                    this.log(`Player ${player.nickname} doesn't see all 4 players in lobby`, 'error');
                }
            }
            
            if (allPlayersInLobby) {
                this.log('Multiplayer lobby test PASSED - All players see each other', 'success');
                return true;
            } else {
                this.log('Multiplayer lobby test FAILED', 'error');
                return false;
            }
        } catch (error) {
            this.log(`Multiplayer lobby test FAILED: ${error.message}`, 'error');
            return false;
        }
    }

    async testChatSystem() {
        this.log('💬 Testing chat system...');
        
        try {
            if (this.players.length < 2) {
                throw new Error('Need at least 2 players for chat test');
            }
            
            const player1 = this.players[0];
            const player2 = this.players[1];
            
            // Clear previous messages
            player1.messages = [];
            player2.messages = [];
            
            // Player 1 sends a chat message
            const testMessage = 'Hello from automated test!';
            player1.ws.send(JSON.stringify({
                type: 'chat',
                text: testMessage
            }));
            
            // Wait for message propagation
            await this.wait(1000);
            
            // Check if player 2 received the chat message
            const chatMessages = player2.messages.filter(m => m.type === 'state' && m.state.chat);
            const lastChatState = chatMessages.pop();
            
            if (lastChatState && lastChatState.state.chat.some(msg => msg.text === testMessage)) {
                this.log('Chat system test PASSED - Messages sync between players', 'success');
                return true;
            } else {
                this.log('Chat system test FAILED - Messages not syncing', 'error');
                return false;
            }
        } catch (error) {
            this.log(`Chat system test FAILED: ${error.message}`, 'error');
            return false;
        }
    }

    async testGameStart() {
        this.log('🎮 Testing game start functionality...');
        
        try {
            // Wait for game to start (should happen automatically with 4 players)
            this.log('Waiting for game to start with 4 players...');
            await this.wait(15000); // Wait up to 15 seconds for game start
            
            // Check if any player received game state
            let gameStarted = false;
            for (const player of this.players) {
                const gameStates = player.messages.filter(m => m.type === 'state' && m.state.phase === 'game');
                if (gameStates.length > 0) {
                    gameStarted = true;
                    this.log(`Player ${player.nickname} entered game phase`);
                }
            }
            
            if (gameStarted) {
                this.log('Game start test PASSED - Game started with 4 players', 'success');
                return true;
            } else {
                this.log('Game start test FAILED - Game did not start', 'error');
                return false;
            }
        } catch (error) {
            this.log(`Game start test FAILED: ${error.message}`, 'error');
            return false;
        }
    }

    async testPlayerActions() {
        this.log('🎯 Testing player actions...');
        
        try {
            if (this.players.length === 0) {
                throw new Error('No players available for action test');
            }
            
            const player = this.players[0];
            
            // Clear previous messages
            player.messages = [];
            
            // Send movement action
            player.ws.send(JSON.stringify({
                type: 'action',
                action: { type: 'move', dir: 'up' }
            }));
            
            // Send bomb action
            player.ws.send(JSON.stringify({
                type: 'action',
                action: { type: 'bomb' }
            }));
            
            // Wait for state updates
            await this.wait(2000);
            
            // Check if we received state updates (indicating server processed actions)
            const stateUpdates = player.messages.filter(m => m.type === 'state');
            
            if (stateUpdates.length > 0) {
                this.log('Player actions test PASSED - Server processed actions', 'success');
                return true;
            } else {
                this.log('Player actions test FAILED - No state updates received', 'error');
                return false;
            }
        } catch (error) {
            this.log(`Player actions test FAILED: ${error.message}`, 'error');
            return false;
        }
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async runAllTests() {
        this.log('🚀 Starting Bomberman automated test suite...');
        
        const tests = [
            { name: 'Basic Connection', fn: () => this.testBasicConnection() },
            { name: 'Multiplayer Lobby', fn: () => this.testMultiplayerLobby() },
            { name: 'Chat System', fn: () => this.testChatSystem() },
            { name: 'Game Start', fn: () => this.testGameStart() },
            { name: 'Player Actions', fn: () => this.testPlayerActions() }
        ];
        
        let passed = 0;
        let failed = 0;
        
        for (const test of tests) {
            this.log(`\n📋 Running test: ${test.name}`);
            try {
                const result = await test.fn();
                if (result) {
                    passed++;
                } else {
                    failed++;
                }
            } catch (error) {
                this.log(`Test ${test.name} threw error: ${error.message}`, 'error');
                failed++;
            }
        }
        
        this.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
        this.log(`📈 Success Rate: ${Math.round((passed / tests.length) * 100)}%`);

        // Cleanup
        this.cleanup();

        return { passed, failed, total: tests.length };
    }

    cleanup() {
        this.log('🧹 Cleaning up test connections...');
        for (const player of this.players) {
            if (player.ws && player.ws.readyState === WebSocket.OPEN) {
                player.ws.close();
            }
        }
    }
}

// Run tests if this script is executed directly
if (require.main === module) {
    const tester = new BombermanTester();
    tester.runAllTests().then(results => {
        if (results.failed === 0) {
            console.log('\n🎉 All tests passed! Bomberman multiplayer is working correctly.');
            process.exit(0);
        } else {
            console.log(`\n💥 ${results.failed} test(s) failed. Please check the implementation.`);
            process.exit(1);
        }
    }).catch(error => {
        console.error('\n💥 Test suite failed:', error.message);
        process.exit(1);
    });
}

module.exports = BombermanTester;
