// Simple WebSocket test for Bomberman
const WebSocket = require('./server/node_modules/ws');

console.log('🚀 Starting simple Bomberman test...');

const ws = new WebSocket('ws://localhost:8080');

ws.on('open', () => {
    console.log('✅ Connected to WebSocket server');
    
    // Send nickname
    ws.send(JSON.stringify({
        type: 'nickname',
        nickname: 'TestPlayer'
    }));
    
    console.log('📤 Sent nickname');
});

ws.on('message', (data) => {
    try {
        const msg = JSON.parse(data);
        console.log('📥 Received:', msg.type, msg);
        
        if (msg.type === 'state' && msg.state.phase === 'lobby') {
            console.log(`🏠 In lobby with ${msg.state.players?.length || 0} players`);
        }
        
        if (msg.type === 'state' && msg.state.phase === 'game') {
            console.log('🎮 Game started!');
            
            // Test movement
            ws.send(JSON.stringify({
                type: 'action',
                action: { type: 'move', dir: 'up' }
            }));
            console.log('📤 Sent movement action');
            
            // Test bomb
            setTimeout(() => {
                ws.send(JSON.stringify({
                    type: 'action',
                    action: { type: 'bomb' }
                }));
                console.log('📤 Sent bomb action');
                
                // Test chat
                setTimeout(() => {
                    ws.send(JSON.stringify({
                        type: 'chat',
                        text: 'Hello from test!'
                    }));
                    console.log('📤 Sent chat message');
                    
                    setTimeout(() => {
                        console.log('✅ Test completed successfully!');
                        ws.close();
                        process.exit(0);
                    }, 1000);
                }, 1000);
            }, 1000);
        }
    } catch (error) {
        console.log('❌ Error parsing message:', error.message);
    }
});

ws.on('error', (error) => {
    console.log('❌ WebSocket error:', error.message);
    process.exit(1);
});

ws.on('close', () => {
    console.log('🔌 Connection closed');
});

// Timeout after 10 seconds
setTimeout(() => {
    console.log('⏰ Test timeout - closing connection');
    ws.close();
    process.exit(0);
}, 10000);
