<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Game Flow Test - Bomberman DOM</title>
  <link rel="stylesheet" href="style.css">
  <style>
    .test-container {
      background: #333;
      padding: 32px;
      border-radius: 8px;
      margin: 32px auto;
      max-width: 900px;
      font-family: monospace;
    }
    .test-section {
      background: #444;
      padding: 16px;
      border-radius: 6px;
      margin-bottom: 16px;
    }
    .test-section h3 {
      color: #4af;
      margin-top: 0;
    }
    .step {
      background: #555;
      padding: 12px;
      border-radius: 4px;
      margin-bottom: 8px;
      border-left: 4px solid #666;
    }
    .step.active {
      border-left-color: #4af;
      background: #556;
    }
    .step.complete {
      border-left-color: #4f4;
      background: #565;
    }
    .step.error {
      border-left-color: #f44;
      background: #655;
    }
    .controls {
      display: flex;
      gap: 10px;
      margin-top: 10px;
    }
    .controls button {
      padding: 8px 12px;
      background: #666;
      color: #fff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .controls button:hover {
      background: #777;
    }
    .state-display {
      background: #222;
      border: 1px solid #666;
      padding: 12px;
      border-radius: 4px;
      white-space: pre-wrap;
      font-size: 11px;
      max-height: 200px;
      overflow-y: auto;
      margin-top: 10px;
    }
    .log {
      background: #222;
      border: 1px solid #666;
      padding: 8px;
      border-radius: 4px;
      height: 150px;
      overflow-y: auto;
      font-size: 11px;
      margin-top: 10px;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h2>🧪 Game Flow Test</h2>
    
    <div class="test-section">
      <h3>Test Steps</h3>
      <div id="step-1" class="step">
        <strong>Step 1:</strong> Connect to WebSocket server
        <div class="controls">
          <button onclick="runStep1()">Connect</button>
        </div>
      </div>
      <div id="step-2" class="step">
        <strong>Step 2:</strong> Send nickname and join lobby
        <div class="controls">
          <button onclick="runStep2()">Join as TestPlayer</button>
        </div>
      </div>
      <div id="step-3" class="step">
        <strong>Step 3:</strong> Wait for game to start (need 2+ players or 10s timeout)
        <div class="controls">
          <button onclick="runStep3()">Add Second Player</button>
          <button onclick="forceGameStart()">Force Game Start</button>
        </div>
      </div>
      <div id="step-4" class="step">
        <strong>Step 4:</strong> Test game rendering
        <div class="controls">
          <button onclick="runStep4()">Test Rendering</button>
          <button onclick="testGameRoute()">Navigate to /game</button>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h3>Current Game State</h3>
      <div id="game-state" class="state-display">No state received yet</div>
    </div>
    
    <div class="test-section">
      <h3>Test Log</h3>
      <div id="test-log" class="log"></div>
      <div class="controls">
        <button onclick="clearLog()">Clear Log</button>
      </div>
    </div>
  </div>

  <script type="module">
    let socket = null;
    let currentState = null;
    let secondSocket = null;
    
    function log(message) {
      const logEl = document.getElementById('test-log');
      const timestamp = new Date().toLocaleTimeString();
      logEl.textContent += `[${timestamp}] ${message}\n`;
      logEl.scrollTop = logEl.scrollHeight;
      console.log(message);
    }
    
    function updateStep(stepNum, status) {
      const step = document.getElementById(`step-${stepNum}`);
      step.className = `step ${status}`;
    }
    
    function updateGameState() {
      const stateEl = document.getElementById('game-state');
      if (currentState) {
        stateEl.textContent = JSON.stringify(currentState, null, 2);
      } else {
        stateEl.textContent = 'No state received yet';
      }
    }
    
    window.runStep1 = () => {
      log('Step 1: Connecting to WebSocket server...');
      updateStep(1, 'active');
      
      socket = new WebSocket('ws://localhost:8080');
      
      socket.onopen = () => {
        log('✅ Connected to WebSocket server');
        updateStep(1, 'complete');
      };
      
      socket.onmessage = (event) => {
        const msg = JSON.parse(event.data);
        log(`📨 Received: ${msg.type}`);
        
        if (msg.type === 'state') {
          currentState = msg.state;
          updateGameState();
          
          // Auto-advance based on state
          if (msg.state.phase === 'lobby' && msg.state.players.length === 1) {
            log('📍 In lobby with 1 player - ready for step 3');
          } else if (msg.state.phase === 'game') {
            log('🎮 Game started! Ready for step 4');
          }
        }
      };
      
      socket.onclose = () => {
        log('❌ WebSocket connection closed');
        updateStep(1, 'error');
      };
      
      socket.onerror = (error) => {
        log(`❌ WebSocket error: ${error}`);
        updateStep(1, 'error');
      };
    };
    
    window.runStep2 = () => {
      if (!socket || socket.readyState !== WebSocket.OPEN) {
        log('❌ Not connected to server');
        return;
      }
      
      log('Step 2: Sending nickname...');
      updateStep(2, 'active');
      
      const msg = { type: 'nickname', nickname: 'TestPlayer' };
      socket.send(JSON.stringify(msg));
      log(`📤 Sent: ${JSON.stringify(msg)}`);
      
      // Wait a moment then mark complete
      setTimeout(() => {
        if (currentState && currentState.players && currentState.players.length > 0) {
          log('✅ Successfully joined lobby');
          updateStep(2, 'complete');
        } else {
          log('❌ Failed to join lobby');
          updateStep(2, 'error');
        }
      }, 1000);
    };
    
    window.runStep3 = () => {
      log('Step 3: Adding second player to trigger game start...');
      updateStep(3, 'active');
      
      // Create second connection
      secondSocket = new WebSocket('ws://localhost:8080');
      
      secondSocket.onopen = () => {
        log('✅ Second player connected');
        const msg = { type: 'nickname', nickname: 'TestPlayer2' };
        secondSocket.send(JSON.stringify(msg));
        log('📤 Second player joined');
      };
      
      secondSocket.onmessage = (event) => {
        const msg = JSON.parse(event.data);
        if (msg.type === 'state' && msg.state.phase === 'game') {
          log('🎮 Game started with 2 players!');
          updateStep(3, 'complete');
        }
      };
    };
    
    window.forceGameStart = () => {
      log('Attempting to force game start by waiting...');
      // The server should start the game after 10 seconds with 1+ players
      let countdown = 10;
      const timer = setInterval(() => {
        log(`⏰ Game will start in ${countdown} seconds...`);
        countdown--;
        if (countdown <= 0) {
          clearInterval(timer);
          if (currentState && currentState.phase === 'game') {
            log('🎮 Game started after timeout!');
            updateStep(3, 'complete');
          } else {
            log('❌ Game did not start after timeout');
            updateStep(3, 'error');
          }
        }
      }, 1000);
    };
    
    window.runStep4 = () => {
      if (!currentState || currentState.phase !== 'game') {
        log('❌ Game not started yet');
        return;
      }
      
      log('Step 4: Testing game rendering...');
      updateStep(4, 'active');
      
      // Import and test the renderer
      import('./src/game/renderer.js').then(renderer => {
        log('✅ Renderer module loaded');
        
        // Create test container for DOM rendering
        const container = document.createElement('div');
        container.style.cssText = 'width: 544px; height: 480px; margin: 20px auto;';
        document.body.appendChild(container);

        try {
          renderer.renderGame(currentState, container);
          log('✅ DOM rendering successful');

          updateStep(4, 'complete');
          log('🎉 All tests passed! Game is working correctly with DOM-only rendering.');

        } catch (error) {
          log(`❌ DOM rendering failed: ${error.message}`);
          updateStep(4, 'error');
        }
        
      }).catch(error => {
        log(`❌ Failed to load renderer: ${error.message}`);
        updateStep(4, 'error');
      });
    };
    
    window.testGameRoute = () => {
      log('🔗 Testing /game route navigation...');
      window.location.href = '/game';
    };
    
    window.clearLog = () => {
      document.getElementById('test-log').textContent = '';
    };
    
    // Initialize
    log('🧪 Game Flow Test initialized');
    log('👆 Click "Connect" to start the test sequence');
  </script>
</body>
</html>
