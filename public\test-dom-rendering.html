<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>DOM Rendering Test</title>
  <link rel="stylesheet" href="style.css">
  <style>
    body {
      font-family: Arial, sans-serif;
      background: #222;
      color: #fff;
      margin: 0;
      padding: 20px;
    }
    .test-container {
      max-width: 800px;
      margin: 0 auto;
    }
    .test-result {
      margin: 10px 0;
      padding: 10px;
      border-radius: 4px;
    }
    .success { background: #2d5a2d; }
    .error { background: #5a2d2d; }
    .info { background: #2d2d5a; }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>🎮 Bomberman DOM Rendering Test</h1>
    <div id="test-results"></div>
    <div id="game-container" style="margin-top: 20px;"></div>
  </div>

  <script type="module">
    const results = document.getElementById('test-results');
    const gameContainer = document.getElementById('game-container');

    function log(message, type = 'info') {
      const div = document.createElement('div');
      div.className = `test-result ${type}`;
      div.textContent = message;
      results.appendChild(div);
      console.log(message);
    }

    async function runTests() {
      log('🚀 Starting DOM rendering tests...', 'info');

      try {
        // Test 1: Import renderer module
        log('Test 1: Importing renderer module...', 'info');
        const renderer = await import('./src/game/renderer.js');
        log('✅ Renderer module imported successfully', 'success');

        // Test 2: Check exports
        log('Test 2: Checking exports...', 'info');
        if (typeof renderer.renderGame === 'function') {
          log('✅ renderGame function found', 'success');
        } else {
          log('❌ renderGame function not found', 'error');
          return;
        }

        if (typeof renderer.renderGameDOM === 'function') {
          log('✅ renderGameDOM function found', 'success');
        } else {
          log('❌ renderGameDOM function not found', 'error');
          return;
        }

        // Test 3: Create test game state
        log('Test 3: Creating test game state...', 'info');
        const testState = {
          map: [
            ['W', 'W', 'W', 'W', 'W'],
            ['W', 'E', 'B', 'E', 'W'],
            ['W', 'B', 'E', 'B', 'W'],
            ['W', 'E', 'B', 'E', 'W'],
            ['W', 'W', 'W', 'W', 'W']
          ],
          players: [
            { id: 'test1', nickname: 'Player1', row: 1, col: 1, alive: true },
            { id: 'test2', nickname: 'Player2', row: 3, col: 3, alive: true }
          ],
          bombs: [
            { id: 'bomb1', row: 2, col: 2, timer: 3000 }
          ],
          explosions: [],
          powerups: [
            { id: 'power1', type: 'speed_up', row: 1, col: 3, collected: false }
          ]
        };
        log('✅ Test game state created', 'success');

        // Test 4: Render game
        log('Test 4: Rendering game with DOM...', 'info');
        renderer.renderGame(testState, gameContainer);
        log('✅ Game rendered successfully with DOM', 'success');

        // Test 5: Check if game board was created
        log('Test 5: Checking rendered elements...', 'info');
        const gameBoard = gameContainer.querySelector('.game-board');
        if (gameBoard) {
          log('✅ Game board element created', 'success');
          
          const tiles = gameBoard.querySelectorAll('.game-tile');
          log(`✅ ${tiles.length} tiles rendered`, 'success');
          
          const players = gameBoard.querySelectorAll('.game-object.player');
          log(`✅ ${players.length} players rendered`, 'success');
          
          const bombs = gameBoard.querySelectorAll('.game-object.bomb');
          log(`✅ ${bombs.length} bombs rendered`, 'success');
          
          const powerups = gameBoard.querySelectorAll('.game-object.powerup');
          log(`✅ ${powerups.length} powerups rendered`, 'success');
          
        } else {
          log('❌ Game board element not found', 'error');
        }

        log('🎉 All tests passed! DOM rendering is working correctly.', 'success');

      } catch (error) {
        log(`❌ Test failed: ${error.message}`, 'error');
        console.error('Full error:', error);
      }
    }

    // Run tests when page loads
    runTests();
  </script>
</body>
</html>
